<!-- ============================================ -->
<!--                   Banner                     -->
<!-- ============================================ -->

<div id="ContactBanner-721">
    <div class="cs-container">
        <span class="cs-int-title">Contact Us</span>
    </div>
    <!--Background Image-->
    <picture class="cs-background">
        <!--Mobile Image-->
        <source media="(max-width: 600px)" srcset="https://csimg.nyc3.cdn.digitaloceanspaces.com/Images%2FMISC%2Fpills.jpg">
        <!--Tablet and above Image-->
        <source media="(min-width: 601px)" srcset="https://csimg.nyc3.cdn.digitaloceanspaces.com/Images%2FMISC%2Fpills.jpg">
        <img loading="lazy" decoding="async" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Images%2FMISC%2Fpills.jpg" alt="pills" width="1280" height="568" aria-hidden="true">
    </picture>
</div>

<!-- ============================================ -->
<!--                   Contact                    -->
<!-- ============================================ -->

<section id="ContactForm-721">
    <div class="cs-container">
        <picture class="cs-picture">
            <source media="(max-width: 600px)" srcset="https://csimg.nyc3.cdn.digitaloceanspaces.com/Images%2FPeople%2Fruuner1-m.jpg">
            <source media="(min-width: 601px)" srcset="https://csimg.nyc3.cdn.digitaloceanspaces.com/Images%2FPeople%2Frunner1.jpg">
            <img decoding="async" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Images%2FPeople%2Frunner1.jpg" alt="runner" width="610" height="425" aria-hidden="true">
        </picture>
        <div class="cs-content">
            <span class="cs-topper">Contact Us</span>
            <h2 class="cs-title">We're Here to Help</h2>
            <p class="cs-text">
                Our dedicated beauty consultants are ready to assist you with personalized skincare recommendations, product inquiries, or any questions about your Pureluxus experience. Reach out to us and discover your path to radiant, luminous skin.
            </p>
            <!--Form-->
            <form class="cs-form" id="cs-form-718-721" name="Contact Form" method="post">
                <label class="cs-label">
                    Name
                    <input class="cs-input" required type="text" id="name-718-721" name="name" placeholder="Name">
                </label>
                <label class="cs-label cs-email">
                    Email
                    <input class="cs-input" required type="text" id="email-718-721" name="email" placeholder="Email">
                </label>
                <label class="cs-label cs-phone">
                    Phone
                    <input class="cs-input" required type="text" id="phone-718-721" name="phone" placeholder="Phone">
                </label>
                <label class="cs-label">
                    Message
                    <textarea class="cs-input cs-textarea" required name="Message" id="message-718-721" placeholder="Write message..."></textarea>
                </label>
                <button class="cs-button-solid cs-submit" type="submit">Send Message</button>
            </form>
        </div>
    </div>
</section>
                         
<!-- ============================================ -->
<!--                   Stats                      -->
<!-- ============================================ -->

<!-- No headers, so no section tag -->
<div id="ContactStrip-721">
    <ul class="cs-stat-group">
        <li class="cs-item">
            <picture class="cs-picture">
                <img class="cs-icon" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Icons%2Fphone-stroke-light.svg" alt="icon" width="35" height="40" loading="lazy" decoding="async" aria-hidden="true">
            </picture>
            <div class="cs-flex-group">
                <span class="cs-header">Customer Care</span>
                <a href="tel:************" class="cs-link">+****************</a>
            </div>
        </li>
        <li class="cs-item">
            <picture class="cs-picture">
                <img class="cs-icon" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Icons%2Fmail-stroke-light.svg" alt="icon" width="40" height="35" loading="lazy" decoding="async" aria-hidden="true">
            </picture>
            <div class="cs-flex-group">
                <span class="cs-header">Email Us</span>
                <a href="mailto:<EMAIL>" class="cs-link"><EMAIL></a>
            </div>
        </li>
        <li class="cs-item">
            <picture class="cs-picture">
                <img class="cs-icon" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Icons%2Fpin-stroke-light.svg" alt="icon" width="40" height="40" loading="lazy" decoding="async" aria-hidden="true">
            </picture>
            <div class="cs-flex-group">
                <span class="cs-header">Visit Our Boutique</span>
                <span class="cs-address">456 Luxury Lane, Beverly Hills, CA 90210,
                    United States</span>
            </div>
        </li>
    </ul>
    <!--SVG Waves Background-->
    <img class="cs-background" src="https://csimg.nyc3.cdn.digitaloceanspaces.com/Images%2FGraphics%2Fwaves-3.svg" alt="wave graphic" width="1920" height="318" loading="lazy" decoding="async" aria-hidden="true">
</div>
              

<style>body.dark-mode .cs-button-solid {
    background-color: var(--secondaryLight);
    color: #1a1a1a;
}
body.dark-mode .cs-button-solid:before {
    background-color: #fff;
}
.cs-button-solid {
    font-family: 'Poppins', sans-serif;
    font-size: 1rem;
    /* 46px - 56px */
    line-height: clamp(2.875rem, 5.5vw, 3.5rem);
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    margin: 0;
    color: #fff;
    min-width: 9.375rem;
    padding: 0 1.5rem;
    background-color: var(--primary);
    border-radius: 0.25rem;
    display: inline-block;
    position: relative;
    z-index: 1;
    /* prevents padding from adding to the width */
    box-sizing: border-box;
    letter-spacing: 0.02em;
}
.cs-button-solid:before {
    content: "";
    position: absolute;
    height: 100%;
    width: 0%;
    background: #E67E5B; /* Darker peach for hover */
    opacity: 1;
    top: 0;
    left: 0;
    z-index: -1;
    border-radius: 0.25rem;
    transition: width 0.3s;
}
.cs-button-solid:hover:before {
    width: 100%;
}
/* ^^^ remove everything above this comment and place in global stylesheet ^^^ */

/*-- -------------------------- -->
<---          Banner            -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
    #ContactBanner-721 {
        /* 175px - 200px top */
        padding: clamp(10.9375rem, 10vw, 12.5rem) 1rem 6.25rem;
        position: relative;
        z-index: 1;
    }
    #ContactBanner-721 .cs-container {
        text-align: center;
        width: 100%;
        max-width: 80rem;
        margin: auto;
        display: flex;
        justify-content: center;
        align-items: flex-start;
        flex-direction: column;
        gap: 1rem;
    }
    #ContactBanner-721 .cs-int-title {
        font-family: 'Playfair Display', serif;
        /* 39px - 61px */
        font-size: clamp(2.4375rem, 6.4vw, 3.8125rem);
        font-weight: 700;
        line-height: 1.1em;
        text-align: inherit;
        margin: 0;
        color: var(--bodyTextColorWhite);
        position: relative;
        letter-spacing: -0.02em;
    }
    #ContactBanner-721 .cs-background {
        width: 100%;
        height: 100%;
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        z-index: -1;
    }
    #ContactBanner-721 .cs-background:before {
        /* background color overlay */
        content: "";
        position: absolute;
        display: block;
        height: 100%;
        width: 100%;
        background: #000;
        opacity: 0.75;
        top: 0;
        left: 0;
        z-index: 1;
    }
    #ContactBanner-721 .cs-background img {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
        /* Makes img tag act as a background image */
        object-fit: cover;
    }
}
/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
    #ContactBanner-721 .cs-background:before {
        opacity: 1;
        background: linear-gradient(
            90.01deg,
            rgba(0, 0, 0, 0.9) 16.86%,
            rgba(0, 0, 0, 0) 100%
        );
    }
}

/*-- -------------------------- -->
<---          Contact           -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
    #ContactForm-721 {
        padding: var(--sectionPadding);
        position: relative;
        z-index: 1;
    }
    #ContactForm-721 .cs-container {
        width: 100%;
        /* changes to 1280px at desktop */
        max-width: 34.375rem;
        margin: auto;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        gap: 3.25rem;
    }
    #ContactForm-721 .cs-picture {
        width: 100%;
        max-width: 40.625rem;
        height: auto;
        /* reset at desktop */
        aspect-ratio: 1.00516351;
        position: relative;
    }
    #ContactForm-721 .cs-picture img {
        width: 100%;
        height: 100%;
        /* makes it act like a background image */
        object-fit: cover;
        position: absolute;
        top: 0;
        left: 0;
    }
    #ContactForm-721 .cs-content {
        /* set text align to left if content needs to be left aligned */
        text-align: left;
        width: 100%;
        max-width: 33.875rem;
        display: flex;
        flex-direction: column;
        /* centers content horizontally, set to flex-start to left align */
        align-items: flex-start;
    }

    #ContactForm-721 .cs-form {
        /* 24px - 48px top and bottom */
        /* 20px - 32px left and right */
        padding: clamp(1.25rem, 4.18vw, 3rem) clamp(1.25rem, 4.18vw, 2rem);
        background-color: #FFF5F3; /* Very light peach background */
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        align-items: center;
        gap: 0.75rem;
    }
    #ContactForm-721 .cs-label {
        /* 14px - 16px */
        font-size: clamp(0.875rem, 1.5vw, 1rem);
        width: 100%;
        color: var(--headerColor);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        gap: 0.25rem;
    }
    #ContactForm-721 .cs-input {
        font-size: 1rem;
        width: 100%;
        height: 3.5rem;
        padding: 0;
        padding-left: 1.5rem;
        color: var(--headerColor);
        background-color: #fff;
        border: none;
        /* prevents padding from adding to height and width */
        box-sizing: border-box;
    }
    #ContactForm-721 .cs-input::placeholder {
        color: #7d799c;
        opacity: 0.6;
    }
    #ContactForm-721 .cs-textarea {
        min-height: 7.5rem;
        padding-top: 1.5rem;
        margin-bottom: 0.75rem;
        font-family: inherit;
    }
    #ContactForm-721 .cs-button-solid {
        font-size: 1rem;
        /* 46px - 56px */
        line-height: clamp(2.875em, 5.5vw, 3.5em);
        text-decoration: none;
        font-weight: 700;
        text-align: center;
        margin: 0;
        color: #fff;
        border: none;
        min-width: 9.375rem;
        padding: 0 1.5rem;
        background-color: var(--primary);
        border-radius: 0.25rem;
        display: inline-block;
        position: relative;
        z-index: 1;
        /* prevents padding from adding to the width */
        box-sizing: border-box;
        transition: color 0.3s;
    }
    #ContactForm-721 .cs-button-solid:before {
        content: "";
        position: absolute;
        height: 100%;
        width: 0%;
        background: #E67E5B; /* Darker peach for hover */
        opacity: 1;
        top: 0;
        left: 0;
        z-index: -1;
        border-radius: 0.25rem;
        transition: width 0.3s;
    }
    #ContactForm-721 .cs-button-solid:hover {
        color: #fff;
    }
    #ContactForm-721 .cs-button-solid:hover:before {
        width: 100%;
    }
    #ContactForm-721 .cs-submit {
        min-width: 17.6875rem;
        border-radius: 0;
    }
}
/* Small Desktop - 1024px */
@media only screen and (min-width: 64rem) {
    #ContactForm-721 .cs-container {
        max-width: 80rem;
        flex-direction: row;
        justify-content: space-between;
        gap: 3.25rem;
    }
    #ContactForm-721 .cs-picture {
        height: 51.875rem;
        aspect-ratio: initial;
        /* sends it to the right in the 2nd position */
        order: 2;
    }
}

/*-- -------------------------- -->
<---           Stats            -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0em) {
    #ContactStrip-721 {
        padding: var(--sectionPadding);
        background-color: var(--primary);
        position: relative;
        z-index: 1;
    }
    #ContactStrip-721 .cs-stat-group {
        width: 100%;
        /* changes to 1280px at desktop */
        max-width: 37.5rem;
        margin: auto;
        padding: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        gap: 2.5rem;
    }
    #ContactStrip-721 .cs-item {
        list-style: none;
        width: 18.125rem;
        margin: 0;
        padding: 0;
        display: flex;
        justify-content: flex-start;
        align-items: center;
    }
    #ContactStrip-721 .cs-item:hover .cs-picture {
        background-color: #fff;
        box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
        transform: scale(1.1);
    }
    #ContactStrip-721 .cs-picture {
        width: 5rem;
        height: 5rem;
        /* 12px - 20px */
        margin-right: clamp(0.75rem, 3vw, 1.25rem);
        border-radius: 50%;
        border: 1px solid var(--primaryLight); /* Light peach border */
        display: flex;
        justify-content: center;
        align-items: center;
        /* prevents flexbox from squishing it */
        flex: none;
        transition:
            background-color 0.3s,
            box-shadow 0.3s,
            transform 0.6s;
    }
    #ContactStrip-721 .cs-flex-group {
        display: flex;
        justify-content: center;
        align-items: flex-start;
        flex-direction: column;
    }
    #ContactStrip-721 .cs-icon {
        width: 2.5rem;
        height: auto;
    }
    #ContactStrip-721 .cs-header {
        font-size: 1.25rem;
        color: var(--bodyTextColorWhite);
        font-weight: 900;
        line-height: 1.2em;
        margin: 0;
        margin-bottom: 0.75rem;
        display: block;
    }
    #ContactStrip-721 .cs-link,
    #ContactStrip-721 .cs-address {
        font-size: var(--bodyTextColor);
        line-height: 1.5em;
        text-decoration: none;
        color: var(--bodyTextColorWhite);
        display: block;
    }
    #ContactStrip-721 .cs-link:hover {
        text-decoration: underline;
    }
    #ContactStrip-721 .cs-background {
        width: 100%;
        height: 100%;
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        z-index: -1;
        /* makes it act like a background image */
        object-fit: cover;
    }
}
/* Tablet - 650px */
@media only screen and (min-width: 40.625em) {
    #ContactStrip-721 .cs-stat-group {
        flex-direction: row;
        flex-wrap: wrap;
        column-gap: 1.25rem;
        row-gap: 2rem;
    }
}
/* Small Desktop - 1024px */
@media only screen and (min-width: 64em) {
    #ContactStrip-721 .cs-stat-group {
        max-width: 80rem;
        flex-wrap: nowrap;
        justify-content: space-evenly;
    }
}

/*-- -------------------------- -->
<---            Map             -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
    #ContactMap-721 {
        min-height: 33.75rem;
        padding: var(--sectionPadding);
        /* prevents padding and border from affecting height and width */
        box-sizing: border-box;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
    }
    #ContactMap-721 .cs-background {
        width: 100%;
        height: 100%;
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        z-index: -1;
    }
    #ContactMap-721 .cs-background img {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
        /* Makes img tag act as a background image */
        object-fit: cover;
    }
}

/*-- -------------------------- -->
<---            FAQ             -->
<--- -------------------------- -*/

/* Mobile - 360px */
@media only screen and (min-width: 0rem) {
    #ContactFAQ-721 {
        position: relative;
        overflow: hidden;
    }
    #ContactFAQ-721 .cs-container {
        width: 100%;
        max-width: 80rem;
        margin: auto;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        /* 40px - 48px */
        gap: clamp(2.5rem, 5vw, 3rem);
    }
    #ContactFAQ-721 .cs-content {
        text-align: left;
        max-width: 33.8125rem;
        /* move section padding to the cs-content so we can make the cs-picture full width */
        padding: var(--sectionPadding);
        padding-top: 0;
        /* prevents padding and border from affecting height and width */
        box-sizing: border-box;
    }

    #ContactFAQ-721 .cs-faq-group {
        padding: 0;
        margin: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
    }
    #ContactFAQ-721 .cs-faq-item {
        list-style: none;
        width: 100%;
        border-bottom: 1px solid #e8e8e8;
        transition: border-bottom 0.3s;
    }
    #ContactFAQ-721 .cs-faq-item.active {
        border-color: var(--primaryLight);
    }
    #ContactFAQ-721 .cs-faq-item.active .cs-button {
        color: var(--primary);
    }
    #ContactFAQ-721 .cs-faq-item.active .cs-button:before {
        background-color: var(--primaryLight);
        transform: rotate(315deg);
    }
    #ContactFAQ-721 .cs-faq-item.active .cs-button:after {
        background-color: var(--primaryLight);
        transform: rotate(-315deg);
    }
    #ContactFAQ-721 .cs-faq-item.active .cs-item-p {
        height: auto;
        /* 20px - 24px bottom */
        padding: 0 0 clamp(1rem, 2vw, 1.5rem) 0;
        opacity: 1;
    }
    #ContactFAQ-721 .cs-button {
        /* 16px - 20px */
        font-size: clamp(1rem, 2vw, 1.25rem);
        line-height: 1.2em;
        text-align: left;
        font-weight: bold;
        /* 16px - 24px */
        padding: clamp(1rem, 2.3vw, 1.5rem) 0;
        border: none;
        background: transparent;
        color: var(--headerColor);
        display: block;
        width: 100%;
        position: relative;
        transition:
            background-color 0.3s,
            color 0.3s;
    }
    #ContactFAQ-721 .cs-button:hover {
        cursor: pointer;
    }
    #ContactFAQ-721 .cs-button:before {
        /* left line */
        content: "";
        width: 0.5rem;
        height: 0.125rem;
        background-color: var(--headerColor);
        opacity: 1;
        border-radius: 50%;
        position: absolute;
        display: block;
        top: 45%;
        right: 0.25rem;
        transform: rotate(45deg);
        /* animate the transform from the left side of the x axis, and the center of the y */
        transform-origin: left center;
        transition: transform 0.5s;
    }
    #ContactFAQ-721 .cs-button:after {
        /* right line */
        content: "";
        width: 0.5rem;
        height: 0.125rem;
        background-color: var(--headerColor);
        opacity: 1;
        border-radius: 50%;
        position: absolute;
        display: block;
        top: 45%;
        right: 0.0625rem;
        transform: rotate(-45deg);
        /* animate the transform from the right side of the x axis, and the center of the y */
        transform-origin: right center;
        transition: transform 0.5s;
    }
    #ContactFAQ-721 .cs-button-text {
        width: 90%;
        display: block;
    }
    #ContactFAQ-721 .cs-item-p {
        /* 14px - 16px */
        font-size: clamp(0.875rem, 1.5vw, 1rem);
        line-height: 1.5em;
        width: 90%;
        max-width: 33.8125rem;
        height: 0;
        margin: 0;
        padding: 0;
        opacity: 0;
        color: var(--bodyTextColor);
        /* clips the text so it doesn't show up */
        overflow: hidden;
        transition:
            opacity 0.3s,
            padding-bottom 0.3s;
    }
    #ContactFAQ-721 .cs-graphic {
        width: 22.5625rem;
        height: auto;
        opacity: 0.5;
        display: none;
        position: absolute;
        left: -11.25rem;
        bottom: 1.875rem;
        /* flips it horizontally */
        transform: scaleX(-1);
    }
    #ContactFAQ-721 .cs-picture {
        display: block;
        position: relative;
        width: 100%;
        height: 16rem;
        z-index: 1;
    }
    #ContactFAQ-721 .cs-picture img {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
        object-fit: cover;
    }
}
/* Tablet - 768px */
@media only screen and (min-width: 48rem) {
    #ContactFAQ-721 {
        /* moved seciton padding back to the section */
        padding: var(--sectionPadding);
    }
    #ContactFAQ-721 .cs-container {
        flex-direction: row;
        justify-content: space-between;
        align-items: flex-start;
    }
    #ContactFAQ-721 .cs-picture {
        width: 50%;
        max-width: 40.625rem;
        /* 500px - 750px */
        height: clamp(31.25rem, 68vw, 46.875rem);
        /* sends it to the right in the 2nd position */
        order: 2;
    }
    #ContactFAQ-721 .cs-content {
        width: 55%;
        padding: 0;
    }
}
/* Desktop - 1600px */
@media only screen and (min-width: 100rem) {
    #ContactFAQ-721 .cs-graphic {
        display: block;
    }
}

</style>

<script>const faqItems = Array.from(document.querySelectorAll('.cs-faq-item'));
        for (const item of faqItems) {
            const onClick = () => {
            item.classList.toggle('active')
        }
        item.addEventListener('click', onClick)
        }
</script>